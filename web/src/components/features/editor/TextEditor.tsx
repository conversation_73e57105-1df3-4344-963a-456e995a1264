import { Textarea } from "@/components/ui/Textarea";
import { useState } from "react";
import { EditorToolbar } from "./EditorToolbar";

export const TextEditor = () => {
  const [rawState, setRawState] = useState<string | undefined>();
  return (
    <div className="flex flex-col">
      <EditorToolbar />
      <Textarea className="resize-none rounded-t-none border-ring ring-ring/50 " />
    </div>
  );
};
