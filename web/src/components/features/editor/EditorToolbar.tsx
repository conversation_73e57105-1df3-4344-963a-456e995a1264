import { Button } from "@/components/ui/Button";
import { Bold } from "lucide-react";

export const EditorToolbar = () => {
  return (
    <div className="rounded-b-none border-input placeholder:text-muted-foreground border-ring ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] md:text-sm">
      <div>
        <Button size="icon" variant="outline">
          <Bold />
        </Button>
      </div>
    </div>
  );
};
